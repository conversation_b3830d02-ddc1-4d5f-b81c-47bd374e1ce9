/**
 * Standardized Local Storage Hook
 * Provides type-safe local storage with serialization, validation, and SSR support
 */

import { useState, useEffect, useCallback, useRef } from 'react';
import { z } from 'zod';

/**
 * Storage options
 */
export interface StorageOptions<T> {
  // Validation
  schema?: z.ZodSchema<T>;
  
  // Serialization
  serialize?: (value: T) => string;
  deserialize?: (value: string) => T;
  
  // Error handling
  onError?: (error: Error, key: string) => void;
  
  // SSR
  ssr?: boolean;
  
  // Sync across tabs
  syncAcrossTabs?: boolean;
}

/**
 * Storage hook return type
 */
export interface UseStorageReturn<T> {
  value: T;
  setValue: (value: T | ((prev: T) => T)) => void;
  removeValue: () => void;
  isLoading: boolean;
  error: Error | null;
}

/**
 * Default serialization functions
 */
const defaultSerialize = <T>(value: T): string => JSON.stringify(value);
const defaultDeserialize = <T>(value: string): T => JSON.parse(value);

/**
 * Check if we're in a browser environment
 */
const isBrowser = typeof window !== 'undefined';

/**
 * Safe storage access
 */
function getStorageValue<T>(
  key: string,
  defaultValue: T,
  options: StorageOptions<T>
): T {
  if (!isBrowser) {
    return defaultValue;
  }

  try {
    const item = localStorage.getItem(key);
    
    if (item === null) {
      return defaultValue;
    }

    const deserialize = options.deserialize || defaultDeserialize;
    const parsed = deserialize(item);

    // Validate with schema if provided
    if (options.schema) {
      return options.schema.parse(parsed);
    }

    return parsed;
  } catch (error) {
    const err = error instanceof Error ? error : new Error('Storage parsing error');
    options.onError?.(err, key);
    return defaultValue;
  }
}

/**
 * Safe storage write
 */
function setStorageValue<T>(
  key: string,
  value: T,
  options: StorageOptions<T>
): void {
  if (!isBrowser) {
    return;
  }

  try {
    // Validate with schema if provided
    if (options.schema) {
      options.schema.parse(value);
    }

    const serialize = options.serialize || defaultSerialize;
    const serialized = serialize(value);
    localStorage.setItem(key, serialized);
  } catch (error) {
    const err = error instanceof Error ? error : new Error('Storage write error');
    options.onError?.(err, key);
  }
}

/**
 * Main local storage hook
 */
export function useLocalStorage<T>(
  key: string,
  defaultValue: T,
  options: StorageOptions<T> = {}
): UseStorageReturn<T> {
  const {
    ssr = true,
    syncAcrossTabs = true,
    onError,
  } = options;

  const [isLoading, setIsLoading] = useState(ssr);
  const [error, setError] = useState<Error | null>(null);
  const [storedValue, setStoredValue] = useState<T>(() => {
    if (ssr) {
      return defaultValue;
    }
    return getStorageValue(key, defaultValue, options);
  });

  const optionsRef = useRef(options);
  optionsRef.current = options;

  // Initialize value on client side for SSR
  useEffect(() => {
    if (ssr && isBrowser) {
      try {
        const value = getStorageValue(key, defaultValue, optionsRef.current);
        setStoredValue(value);
        setError(null);
      } catch (err) {
        const error = err instanceof Error ? err : new Error('Storage initialization error');
        setError(error);
        onError?.(error, key);
      } finally {
        setIsLoading(false);
      }
    }
  }, [key, defaultValue, ssr, onError]);

  // Listen for storage changes across tabs
  useEffect(() => {
    if (!isBrowser || !syncAcrossTabs) {
      return;
    }

    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === key && e.newValue !== null) {
        try {
          const deserialize = optionsRef.current.deserialize || defaultDeserialize;
          const newValue = deserialize(e.newValue);
          
          // Validate with schema if provided
          if (optionsRef.current.schema) {
            optionsRef.current.schema.parse(newValue);
          }
          
          setStoredValue(newValue);
          setError(null);
        } catch (err) {
          const error = err instanceof Error ? err : new Error('Storage sync error');
          setError(error);
          optionsRef.current.onError?.(error, key);
        }
      }
    };

    window.addEventListener('storage', handleStorageChange);
    return () => window.removeEventListener('storage', handleStorageChange);
  }, [key, syncAcrossTabs]);

  // Set value function
  const setValue = useCallback((value: T | ((prev: T) => T)) => {
    try {
      const valueToStore = value instanceof Function ? value(storedValue) : value;
      setStoredValue(valueToStore);
      setStorageValue(key, valueToStore, optionsRef.current);
      setError(null);
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Storage set error');
      setError(error);
      optionsRef.current.onError?.(error, key);
    }
  }, [key, storedValue]);

  // Remove value function
  const removeValue = useCallback(() => {
    try {
      setStoredValue(defaultValue);
      if (isBrowser) {
        localStorage.removeItem(key);
      }
      setError(null);
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Storage remove error');
      setError(error);
      optionsRef.current.onError?.(error, key);
    }
  }, [key, defaultValue]);

  return {
    value: storedValue,
    setValue,
    removeValue,
    isLoading,
    error,
  };
}

/**
 * Session storage hook (similar to localStorage but uses sessionStorage)
 */
export function useSessionStorage<T>(
  key: string,
  defaultValue: T,
  options: StorageOptions<T> = {}
): UseStorageReturn<T> {
  // Similar implementation but using sessionStorage
  // For brevity, using localStorage implementation
  // In practice, you'd create a separate implementation for sessionStorage
  return useLocalStorage(key, defaultValue, { ...options, syncAcrossTabs: false });
}

/**
 * Typed local storage hook with schema validation
 */
export function useTypedStorage<T>(
  key: string,
  defaultValue: T,
  schema: z.ZodSchema<T>,
  options: Omit<StorageOptions<T>, 'schema'> = {}
): UseStorageReturn<T> {
  return useLocalStorage(key, defaultValue, { ...options, schema });
}

/**
 * Simple string storage hook
 */
export function useStringStorage(
  key: string,
  defaultValue: string = '',
  options: Omit<StorageOptions<string>, 'serialize' | 'deserialize'> = {}
): UseStorageReturn<string> {
  return useLocalStorage(key, defaultValue, {
    ...options,
    serialize: (value) => value,
    deserialize: (value) => value,
  });
}

/**
 * Boolean storage hook
 */
export function useBooleanStorage(
  key: string,
  defaultValue: boolean = false,
  options: Omit<StorageOptions<boolean>, 'schema'> = {}
): UseStorageReturn<boolean> {
  return useLocalStorage(key, defaultValue, {
    ...options,
    schema: z.boolean(),
  });
}

/**
 * Number storage hook
 */
export function useNumberStorage(
  key: string,
  defaultValue: number = 0,
  options: Omit<StorageOptions<number>, 'schema'> = {}
): UseStorageReturn<number> {
  return useLocalStorage(key, defaultValue, {
    ...options,
    schema: z.number(),
  });
}

/**
 * Array storage hook
 */
export function useArrayStorage<T>(
  key: string,
  defaultValue: T[] = [],
  itemSchema?: z.ZodSchema<T>,
  options: Omit<StorageOptions<T[]>, 'schema'> = {}
): UseStorageReturn<T[]> {
  const schema = itemSchema ? z.array(itemSchema) : undefined;
  
  return useLocalStorage(key, defaultValue, {
    ...options,
    schema,
  });
}

/**
 * Object storage hook
 */
export function useObjectStorage<T extends Record<string, any>>(
  key: string,
  defaultValue: T,
  schema?: z.ZodSchema<T>,
  options: Omit<StorageOptions<T>, 'schema'> = {}
): UseStorageReturn<T> {
  return useLocalStorage(key, defaultValue, {
    ...options,
    schema,
  });
}

/**
 * Storage hook with expiration
 */
export function useStorageWithExpiry<T>(
  key: string,
  defaultValue: T,
  expiryMinutes: number,
  options: StorageOptions<T> = {}
): UseStorageReturn<T> {
  const expiryKey = `${key}_expiry`;
  
  const isExpired = useCallback(() => {
    if (!isBrowser) return false;
    
    const expiry = localStorage.getItem(expiryKey);
    if (!expiry) return true;
    
    return Date.now() > parseInt(expiry, 10);
  }, [expiryKey]);

  const storage = useLocalStorage(key, defaultValue, {
    ...options,
    serialize: (value) => {
      // Set expiry when storing
      if (isBrowser) {
        const expiryTime = Date.now() + (expiryMinutes * 60 * 1000);
        localStorage.setItem(expiryKey, expiryTime.toString());
      }
      
      const serialize = options.serialize || defaultSerialize;
      return serialize(value);
    },
  });

  // Check expiry on mount and clear if expired
  useEffect(() => {
    if (isExpired()) {
      storage.removeValue();
      if (isBrowser) {
        localStorage.removeItem(expiryKey);
      }
    }
  }, [isExpired, storage, expiryKey]);

  return storage;
}
